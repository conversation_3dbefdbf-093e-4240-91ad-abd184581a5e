# Test Server Script
# This script tests the MiWebServer endpoints and server identifier

Write-Host "🚀 Testing MiWebServer..." -ForegroundColor Cyan

# Function to test an endpoint
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Description
    )
    
    Write-Host "📡 Testing: $Description" -ForegroundColor Yellow
    Write-Host "   URL: $Url" -ForegroundColor Gray
    
    try {
        $response = Invoke-WebRequest -Uri $Url -Method GET -ErrorAction Stop
        Write-Host "   ✅ Status: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "   📄 Content: $($response.Content)" -ForegroundColor White
        
        # Check for custom server header
        $serverHeader = $response.Headers["Server"]
        if ($serverHeader) {
            Write-Host "   🏷️  Server: $serverHeader" -ForegroundColor Cyan
        }
        
        Write-Host ""
    }
    catch {
        Write-Host "   ❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
    }
}

# Function to test 404 error (to see custom error handler)
function Test-404Error {
    Write-Host "🔍 Testing 404 Error (Custom Error Handler)..." -ForegroundColor Yellow
    Write-Host "   URL: http://localhost:8000/nonexistent" -ForegroundColor Gray
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/nonexistent" -Method GET -ErrorAction Stop
    }
    catch {
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            $reader.Close()
            
            Write-Host "   ✅ Status: $statusCode (Expected 404)" -ForegroundColor Green
            Write-Host "   📄 Error Response:" -ForegroundColor White
            Write-Host "   $responseBody" -ForegroundColor Gray
            
            # Check server header in error response
            $serverHeader = $_.Exception.Response.Headers["Server"]
            if ($serverHeader) {
                Write-Host "   🏷️  Server: $serverHeader" -ForegroundColor Cyan
            }
        } else {
            Write-Host "   ❌ Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
        }
        Write-Host ""
    }
}

# Wait for server to start
Write-Host "⏳ Waiting for server to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

# Test endpoints
Test-Endpoint -Url "http://localhost:8000/" -Description "Root endpoint"
Test-Endpoint -Url "http://localhost:8000/health" -Description "Health endpoint"

# Test 404 error to see custom error handler
Test-404Error

Write-Host "🎉 Testing completed!" -ForegroundColor Green
Write-Host "💡 The server identifier should now show 'MiWebServer/1.0.0' instead of 'oatpp/1.3.0'" -ForegroundColor Cyan
