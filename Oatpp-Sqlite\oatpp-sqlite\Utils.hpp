/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#ifndef oatpp_sqlite_Utils_hpp
#define oatpp_sqlite_Utils_hpp

#include "QueryResult.hpp"

namespace oatpp { namespace sqlite {

/**
 * Util methods.
 */
class Utils {
public:

  /**
   * Get row id following the last insert operation on the connection.
   * @param connection - &id:oatpp::sqlite::Connection;.
   * @return
   */
  static v_int64 getLastInsertRowId(const provider::ResourceHandle<orm::Connection>& connection);

};

}}

#endif // oatpp_sqlite_Utils_hpp
