/**
 * @file      HelloController.hpp
 * <AUTHOR> Name (<EMAIL>)
 * @brief     Hello Controller for MiWebServer
 * @version   1.0.0
 * @date      24-06-2025
 * @copyright 2025, your company / association / school
 */

#ifndef HELLO_CONTROLLER_HPP_
#define HELLO_CONTROLLER_HPP_

#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/web/protocol/http/Http.hpp"

#include OATPP_CODEGEN_BEGIN(ApiController)

/**
 * @brief Hello Controller class for handling HTTP requests
 */
class HelloController : public oatpp::web::server::api::ApiController {
public:
  /**
   * @brief Constructor
   * @param objectMapper Object mapper for JSON serialization
   */
  HelloController(const std::shared_ptr<oatpp::data::mapping::ObjectMapper>& objectMapper)
    : oatpp::web::server::api::ApiController(objectMapper) {}

  /**
   * @brief Root endpoint - Welcome message
   * @return HTTP response with welcome message
   */
  ENDPOINT("GET", "/", root) {
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200, 
                                  "Welcome to MiWebServer - Microstack Technology!");
    response->putHeader("Server", "MiWebServer/1.0.0");
    return response;
  }

  /**
   * @brief Health check endpoint
   * @return HTTP response with health status in JSON format
   */
  ENDPOINT("GET", "/health", health) {
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200, 
                                  "{\"status\":\"healthy\",\"server\":\"MiWebServer/1.0.0\"}");
    response->putHeader("Server", "MiWebServer/1.0.0");
    response->putHeader("Content-Type", "application/json");
    return response;
  }

  /**
   * @brief API information endpoint
   * @return HTTP response with API information
   */
  ENDPOINT("GET", "/api/info", apiInfo) {
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200, 
                                  "{\"api\":\"MiWebServer API\",\"version\":\"1.0.0\",\"endpoints\":[\"/\",\"/health\",\"/api/info\"]}");
    response->putHeader("Server", "MiWebServer/1.0.0");
    response->putHeader("Content-Type", "application/json");
    return response;
  }
};

#include OATPP_CODEGEN_END(ApiController)

#endif // HELLO_CONTROLLER_HPP_
