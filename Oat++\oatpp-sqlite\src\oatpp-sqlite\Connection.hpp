/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#ifndef oatpp_sqlite_Connection_hpp
#define oatpp_sqlite_Connection_hpp

#include "oatpp/orm/Connection.hpp"
#include "oatpp/core/Types.hpp"

#include <sqlite3.h>

namespace oatpp { namespace sqlite {

/**
 * SQLite database connection.
 */
class Connection : public oatpp::orm::Connection {
private:
  sqlite3* m_handle;
  bool m_autoCommit;
public:

  /**
   * Constructor.
   * @param handle - sqlite3 handle.
   */
  Connection(sqlite3* handle);

  /**
   * Virtual destructor.
   */
  ~Connection();

  /**
   * Get sqlite3 handle.
   * @return - sqlite3 handle.
   */
  sqlite3* getHandle();

  /**
   * Begin transaction.
   */
  void begin() override;

  /**
   * Commit transaction.
   */
  void commit() override;

  /**
   * Rollback transaction.
   */
  void rollback() override;

  /**
   * Check if connection is in auto-commit mode.
   * @return - `true` if auto-commit is enabled.
   */
  bool isAutoCommit() override;

  /**
   * Check if connection is closed.
   * @return - `true` if connection is closed.
   */
  bool isClosed() override;

  /**
   * Close connection.
   */
  void close() override;

};

}}

#endif // oatpp_sqlite_Connection_hpp
