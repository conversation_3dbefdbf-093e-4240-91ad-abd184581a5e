/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#ifndef oatpp_sqlite_Executor_hpp
#define oatpp_sqlite_Executor_hpp

#include "ConnectionProvider.hpp"
#include "oatpp/orm/Executor.hpp"

namespace oatpp { namespace sqlite {

/**
 * SQLite database executor.
 */
class Executor : public oatpp::orm::Executor {
private:
  std::shared_ptr<ConnectionProvider> m_connectionProvider;
public:

  /**
   * Constructor.
   * @param connectionProvider - database connection provider.
   */
  Executor(const std::shared_ptr<ConnectionProvider>& connectionProvider);

  /**
   * Create shared Executor.
   * @param connectionProvider - database connection provider.
   * @return - `std::shared_ptr` to Executor.
   */
  static std::shared_ptr<Executor> createShared(const std::shared_ptr<ConnectionProvider>& connectionProvider);

  /**
   * Get connection provider.
   * @return - connection provider.
   */
  std::shared_ptr<oatpp::provider::Provider<oatpp::orm::Connection>> getConnectionProvider() override;

  /**
   * Execute query.
   * @param queryTemplate - query template.
   * @param params - query parameters.
   * @param connection - database connection.
   * @return - query result.
   */
  std::shared_ptr<QueryResult> execute(const oatpp::data::share::StringTemplate& queryTemplate,
                                      const std::unordered_map<oatpp::String, oatpp::Void>& params,
                                      const std::shared_ptr<oatpp::orm::Connection>& connection) override;

  /**
   * Begin transaction.
   * @param connection - database connection.
   * @return - transaction object.
   */
  std::shared_ptr<oatpp::orm::Transaction> begin(const std::shared_ptr<oatpp::orm::Connection>& connection) override;

  /**
   * Get schema version.
   * @param suffix - schema suffix.
   * @param connection - database connection.
   * @return - schema version.
   */
  v_int64 getSchemaVersion(const oatpp::String& suffix, const std::shared_ptr<oatpp::orm::Connection>& connection) override;

  /**
   * Migrate schema.
   * @param script - migration script.
   * @param newVersion - new schema version.
   * @param suffix - schema suffix.
   * @param connection - database connection.
   */
  void migrateSchema(const oatpp::String& script, v_int64 newVersion, const oatpp::String& suffix, const std::shared_ptr<oatpp::orm::Connection>& connection) override;

};

}}

#endif // oatpp_sqlite_Executor_hpp
