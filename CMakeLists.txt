cmake_minimum_required(VERSION 4.0.3)
project(MiWebServer VERSION 0.1.0 LANGUAGES C CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

add_subdirectory(Oat++)
# add_subdirectory(Oatpp-Sqlite)  # Temporarily disabled due to compatibility issues

# Component build options

add_executable(MiWebServer
    main.cpp
)
# Find all cpp files recursively
file(GLOB_RECURSE CPP_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/components/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/Service/*.cpp"
)

# Add sources to executable
target_sources(MiWebServer PRIVATE ${CPP_SOURCES})
# Include directories
target_include_directories(MiWebServer PRIVATE
    components/Microsystem
    Controller
    Service
    Dto
    Oatpp-Sqlite
)

# Find SQLite3
find_package(PkgConfig QUIET)
if(PKG_CONFIG_FOUND)
    pkg_check_modules(SQLITE3 QUIET sqlite3)
endif()

if(NOT SQLITE3_FOUND)
    find_path(SQLITE3_INCLUDE_DIR sqlite3.h)
    find_library(SQLITE3_LIBRARY sqlite3)

    if(SQLITE3_INCLUDE_DIR AND SQLITE3_LIBRARY)
        set(SQLITE3_FOUND TRUE)
        set(SQLITE3_INCLUDE_DIRS ${SQLITE3_INCLUDE_DIR})
        set(SQLITE3_LIBRARIES ${SQLITE3_LIBRARY})
    endif()
endif()

# Use embedded SQLite if system SQLite is not found
if(NOT SQLITE3_FOUND)
    message(STATUS "SQLite3 not found, using embedded version from Oatpp-Sqlite")
    set(SQLITE3_INCLUDE_DIRS ${CMAKE_CURRENT_SOURCE_DIR}/Oatpp-Sqlite/sqlite)
    set(SQLITE3_LIBRARIES "")
    set(USE_EMBEDDED_SQLITE TRUE)

    # Add embedded SQLite source
    target_sources(MiWebServer PRIVATE
        Oatpp-Sqlite/sqlite/sqlite3.c
    )

    target_compile_definitions(MiWebServer PRIVATE
        SQLITE_ENABLE_JSON1
        SQLITE_ENABLE_RTREE
        SQLITE_ENABLE_FTS5
    )
else()
    message(STATUS "Found SQLite3: ${SQLITE3_LIBRARIES}")
    set(USE_EMBEDDED_SQLITE FALSE)
endif()

# Include SQLite headers
target_include_directories(MiWebServer PRIVATE ${SQLITE3_INCLUDE_DIRS})

# Link to Oat++ and SQLite
target_link_libraries(MiWebServer PRIVATE
    oatpp
    ${SQLITE3_LIBRARIES}
)