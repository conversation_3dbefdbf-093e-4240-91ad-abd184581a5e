/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#include "Executor.hpp"
#include "Connection.hpp"

namespace oatpp { namespace sqlite {

Executor::Executor(const std::shared_ptr<ConnectionProvider>& connectionProvider)
  : m_connectionProvider(connectionProvider)
{}

std::shared_ptr<Executor> Executor::createShared(const std::shared_ptr<ConnectionProvider>& connectionProvider) {
  return std::make_shared<Executor>(connectionProvider);
}

std::shared_ptr<oatpp::provider::Provider<oatpp::orm::Connection>> Executor::getConnectionProvider() {
  return m_connectionProvider;
}

std::shared_ptr<oatpp::orm::QueryResult> Executor::execute(const oatpp::data::share::StringTemplate& queryTemplate,
                                                          const std::unordered_map<oatpp::String, oatpp::Void>& params,
                                                          const std::shared_ptr<oatpp::orm::Connection>& connection) {
  
  auto sqliteConnection = std::static_pointer_cast<Connection>(connection);
  sqlite3* handle = sqliteConnection->getHandle();
  
  // Simple implementation - just execute the query
  oatpp::String query = queryTemplate.format(params);
  
  sqlite3_stmt* stmt;
  int result = sqlite3_prepare_v2(handle, query->c_str(), -1, &stmt, nullptr);
  
  if(result != SQLITE_OK) {
    oatpp::String error = sqlite3_errmsg(handle);
    throw std::runtime_error("[oatpp::sqlite::Executor::execute()]: " + *error);
  }
  
  // Execute the statement
  result = sqlite3_step(stmt);
  
  // Create a simple result object
  auto queryResult = std::make_shared<oatpp::orm::QueryResult>();
  
  if(result == SQLITE_ROW || result == SQLITE_DONE) {
    // Success
  } else {
    oatpp::String error = sqlite3_errmsg(handle);
    sqlite3_finalize(stmt);
    throw std::runtime_error("[oatpp::sqlite::Executor::execute()]: " + *error);
  }
  
  sqlite3_finalize(stmt);
  return queryResult;
}

std::shared_ptr<oatpp::orm::Transaction> Executor::begin(const std::shared_ptr<oatpp::orm::Connection>& connection) {
  connection->begin();
  return std::make_shared<oatpp::orm::Transaction>(connection, shared_from_this());
}

v_int64 Executor::getSchemaVersion(const oatpp::String& suffix, const std::shared_ptr<oatpp::orm::Connection>& connection) {
  // Simple implementation - return 0 for now
  return 0;
}

void Executor::migrateSchema(const oatpp::String& script, v_int64 newVersion, const oatpp::String& suffix, const std::shared_ptr<oatpp::orm::Connection>& connection) {
  auto sqliteConnection = std::static_pointer_cast<Connection>(connection);
  sqlite3* handle = sqliteConnection->getHandle();
  
  char* errorMessage = nullptr;
  int result = sqlite3_exec(handle, script->c_str(), nullptr, nullptr, &errorMessage);
  
  if(result != SQLITE_OK) {
    oatpp::String error = errorMessage;
    sqlite3_free(errorMessage);
    throw std::runtime_error("[oatpp::sqlite::Executor::migrateSchema()]: " + *error);
  }
}

}}
