const vscode = require('vscode');

function activate(context) {
    // Create status bar item
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBarItem.text = "$(trash) Clean";
    statusBarItem.tooltip = "Clean Build Directory";
    statusBarItem.command = "cleanButton.clean";
    statusBarItem.show();

    // Register the clean command
    const cleanCommand = vscode.commands.registerCommand('cleanButton.clean', async () => {
        try {
            // Show progress
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "Cleaning build directory...",
                cancellable: false
            }, async (progress) => {
                // Execute the clean task
                await vscode.commands.executeCommand('workbench.action.tasks.runTask', 'Clean Build Directory');
            });
        } catch (error) {
            vscode.window.showErrorMessage(`Clean failed: ${error.message}`);
        }
    });

    context.subscriptions.push(statusBarItem);
    context.subscriptions.push(cleanCommand);
}

function deactivate() {}

module.exports = {
    activate,
    deactivate
};
