#include "oatpp/network/Server.hpp"
#include "oatpp/network/tcp/server/ConnectionProvider.hpp"
#include "oatpp/network/Address.hpp"
#include "oatpp/web/server/HttpConnectionHandler.hpp"
#include "oatpp/web/server/HttpRouter.hpp"
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/web/protocol/http/Http.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/component.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/web/server/handler/ErrorHandler.hpp"
#include "oatpp/web/protocol/http/outgoing/BufferBody.hpp"

// Include controllers and handlers
#include "HelloController.hpp"
#include "CustomErrorHandler.hpp"

class AppComponent {
public:
  // Create the httpRouter component
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, httpRouter)([] {
    return oatpp::web::server::HttpRouter::createShared();
  }());

  // Create the serverConnectionProvider component
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, serverConnectionProvider)([] {
    return oatpp::network::tcp::server::ConnectionProvider::createShared({ "0.0.0.0", 8000, oatpp::network::Address::IP_4 });
  }());

  // Create the serverConnectionHandler component with custom error handler
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, serverConnectionHandler)([] {
    OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, router); //Get the router component (httpRouter) and  assign it to router
    auto connectionHandler = oatpp::web::server::HttpConnectionHandler::createShared(router);
    connectionHandler->setErrorHandler(std::make_shared<CustomErrorHandler>());
    return connectionHandler;
  }());
};

void run() {
  AppComponent components;

  // Get components (httpRouter, serverConnectionProvider, serverConnectionHandler)
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, router);
  OATPP_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, connectionProvider);
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, connectionHandler);

  // Create and add controller
  auto helloController = std::make_shared<HelloController>(
    oatpp::parser::json::mapping::ObjectMapper::createShared()
  );
  router->addController(helloController);

  // Create and run server
  oatpp::network::Server server(connectionProvider, connectionHandler);
  OATPP_LOGI("MiWebServer", "Running on port %s...", connectionProvider->getProperty("port").toString()->c_str());
  OATPP_LOGI("MiWebServer", "Server identifier: MiWebServer/1.0.0");
  server.run();
}

int main(int argc, const char* argv[]) {
  oatpp::base::Environment::init();
  run();
  oatpp::base::Environment::destroy();
  return 0;
}


