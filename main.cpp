#include "oatpp/network/Server.hpp"
#include "oatpp/network/tcp/server/ConnectionProvider.hpp"
#include "oatpp/network/Address.hpp"
#include "oatpp/web/server/HttpConnectionHandler.hpp"
#include "oatpp/web/server/HttpRouter.hpp"
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/web/protocol/http/Http.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/component.hpp"
#include "oatpp/core/macro/codegen.hpp"

class AppComponent {
public:
// Create the httpRouter component
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, httpRouter)([]
    {
      return oatpp::web::server::HttpRouter::createShared();
    }());
  // Create the serverConnectionProvider component
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, serverConnectionProvider)(
    [] {
      return oatpp::network::tcp::server::ConnectionProvider::createShared({ "0.0.0.0", 8000, oatpp::network::Address::IP_4 });
    }());
  // Create the serverConnectionHandler component
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, serverConnectionHandler)(
    [] {
      OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, router); // Get the router component and assign it to router
      return oatpp::web::server::HttpConnectionHandler::createShared(router);
    }());
};

void run() {
  AppComponent components;
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, router);/ Get the router component and assign it to router
  OATPP_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, connectionProvider);
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, connectionHandler);
  oatpp::network::Server server(connectionProvider, connectionHandler);
  server.run();
}

int main(int argc, const char* argv[]) {
  oatpp::base::Environment::init();
  run();
  oatpp::base::Environment::destroy();
  return 0;
}


