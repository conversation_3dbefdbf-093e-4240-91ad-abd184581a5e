#include "oatpp/network/Server.hpp"
#include "oatpp/network/tcp/server/ConnectionProvider.hpp"
#include "oatpp/network/Address.hpp"
#include "oatpp/web/server/HttpConnectionHandler.hpp"
#include "oatpp/web/server/HttpRouter.hpp"
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/web/protocol/http/Http.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/component.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/web/server/handler/ErrorHandler.hpp"
#include "oatpp/web/protocol/http/outgoing/BufferBody.hpp"

#include OATPP_CODEGEN_BEGIN(ApiController)

// Custom Error Handler to change server identifier
class CustomErrorHandler : public oatpp::web::server::handler::ErrorHandler {
public:

  std::shared_ptr<oatpp::web::protocol::http::outgoing::Response>
  handleError(const oatpp::web::protocol::http::Status& status,
              const oatpp::String& message,
              const Headers& headers) override {

    oatpp::data::stream::BufferOutputStream stream;
    stream << "server=MiWebServer/1.0.0\n";  // Custom server identifier
    stream << "code=" << status.code << "\n";
    stream << "description=" << status.description << "\n";
    stream << "message=" << message << "\n";

    auto response = oatpp::web::protocol::http::outgoing::Response::createShared(
      status,
      oatpp::web::protocol::http::outgoing::BufferBody::createShared(stream.toString())
    );

    response->putHeader("Server", "MiWebServer/1.0.0");  // Custom server header
    response->putHeader(oatpp::web::protocol::http::Header::CONNECTION,
                       oatpp::web::protocol::http::Header::Value::CONNECTION_CLOSE);

    for(const auto& pair : headers.getAll()) {
      response->putHeader_Unsafe(pair.first, pair.second);
    }

    return response;
  }
};

// Hello Controller with custom endpoints
class HelloController : public oatpp::web::server::api::ApiController {
public:
  HelloController(const std::shared_ptr<oatpp::data::mapping::ObjectMapper>& objectMapper)
    : oatpp::web::server::api::ApiController(objectMapper) {}

  ENDPOINT("GET", "/", root) {
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200,
                                  "Welcome to MiWebServer - Microstack Technology!");
    response->putHeader("Server", "MiWebServer/1.0.0");
    return response;
  }

  ENDPOINT("GET", "/health", health) {
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200,
                                  "{\"status\":\"healthy\",\"server\":\"MiWebServer/1.0.0\"}");
    response->putHeader("Server", "MiWebServer/1.0.0");
    response->putHeader("Content-Type", "application/json");
    return response;
  }
};

#include OATPP_CODEGEN_END(ApiController)

class AppComponent {
public:
  // Create the httpRouter component
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, httpRouter)([] {
    return oatpp::web::server::HttpRouter::createShared();
  }());

  // Create the serverConnectionProvider component
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, serverConnectionProvider)([] {
    return oatpp::network::tcp::server::ConnectionProvider::createShared({ "0.0.0.0", 8000, oatpp::network::Address::IP_4 });
  }());

  // Create the serverConnectionHandler component with custom error handler
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, serverConnectionHandler)([] {
    OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, router); //Get the router component (httpRouter) and  assign it to router
    auto connectionHandler = oatpp::web::server::HttpConnectionHandler::createShared(router);
    connectionHandler->setErrorHandler(std::make_shared<CustomErrorHandler>());
    return connectionHandler;
  }());
};

void run() {
  AppComponent components;

  // Get components (httpRouter, serverConnectionProvider, serverConnectionHandler)
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, router);
  OATPP_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, connectionProvider);
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, connectionHandler);

  // // Create and add controller
  // auto helloController = std::make_shared<HelloController>(
  //   oatpp::parser::json::mapping::ObjectMapper::createShared()
  // );
  // router->addController(helloController);

  // Create and run server
  oatpp::network::Server server(connectionProvider, connectionHandler);
  OATPP_LOGI("MiWebServer", "Running on port %s...", connectionProvider->getProperty("port").toString()->c_str());
  OATPP_LOGI("MiWebServer", "Server identifier: MiWebServer/1.0.0");
  server.run();
}

int main(int argc, const char* argv[]) {
  oatpp::base::Environment::init();
  run();
  oatpp::base::Environment::destroy();
  return 0;
}


