{"artifacts": [{"path": "MiWebServer.exe"}, {"path": "MiWebServer.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "target_include_directories", "target_sources"], "files": ["CMakeLists.txt", "Oat++/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 12, "parent": 0}, {"command": 1, "file": 0, "line": 29, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 328, "parent": 3}, {"command": 2, "file": 0, "line": 23, "parent": 0}, {"command": 3, "file": 0, "line": 21, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}], "includes": [{"backtrace": 5, "path": "D:/MiWebApp/WebServerApp/components/Microsystem"}, {"backtrace": 5, "path": "D:/MiWebApp/WebServerApp/controller"}, {"backtrace": 2, "path": "D:/MiWebApp/WebServerApp/Oat++"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1]}], "dependencies": [{"backtrace": 2, "id": "oatpp::@103db6bc44d64c5e8dd4"}], "id": "MiWebServer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 2, "fragment": "Oat++\\liboatpp.a", "role": "libraries"}, {"backtrace": 4, "fragment": "-lwsock32", "role": "libraries"}, {"backtrace": 4, "fragment": "-lws2_32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "MiWebServer", "nameOnDisk": "MiWebServer.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "components/Microsystem/Microsystem.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}