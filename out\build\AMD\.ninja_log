# ninja log v7
32	463	7722268550799012	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj	a6631bcd383c220b
138	534	7722268551862377	Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj	54b57418cf946cd1
112	560	7722268551594363	Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj	dd9d9cd60b2e6869
119	571	7722268551666525	Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj	5da683dd99b52fef
150	587	7722268551981225	Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj	765a13ed7743ce38
5	720	7722268550530043	Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj	d5f921724202bf4f
178	734	7722268552261351	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj	4ca80235b7cc800e
21	776	7722268550687196	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj	7e0f5f29fea8424
255	791	7722268553032385	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj	12e963b6592c18bd
64	821	7722268551121128	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj	a31df5a619c5ab4b
14	841	7722268550616203	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj	7672900758c54b7
45	861	7722268550926475	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj	f3be4aeec41eef32
69	885	7722268551172428	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj	350f3b162bc348de
280	909	7722268553280420	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj	f267895a7bc6333d
105	932	7722268551527402	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj	2a22d10db4e9b9bb
229	949	7722268552772199	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj	57df52527929b09f
56	969	7722268551044349	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj	ad25555d350ba67f
79	995	7722268551270050	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj	1d6ab0a7cf486610
25	1023	7722268550727872	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj	4e724f15a584f9c4
465	1057	7722268555133408	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj	a5fb4115a3813e9e
172	1077	7722268552193962	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj	cf9caadd1caf326a
239	1087	7722268552866158	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj	a2c569eb3083fbe4
201	1116	7722268552485274	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj	560fce0e2a8d8289
534	1133	7722268555817632	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj	c211c62d4b6fe81f
302	1152	7722268553492639	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj	a1fd9cee5f5ab369
193	1167	7722268552407402	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj	4ad7cdb4683ed1e9
158	1181	7722268552059028	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj	1f3d3f09ad8c47c
560	1192	7722268556081539	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj	6fe6d242d03b6ba8
84	1217	7722268551321473	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj	6f17f6bc3f75cad8
49	1227	7722268550967409	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj	792bd728bf995b19
95	1259	7722268551429585	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj	1c7cbbceedd9d695
289	1368	7722268553373506	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj	2c44553129b08cb
265	1388	7722268553125336	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj	5d5027671ee3aca8
949	1468	7722268559970009	Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj	9e85c1c0f6f3bd8f
1024	1507	7722268560715647	Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj	a3e2e57267ce9786
734	1549	7722268557818724	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj	d50f8a03aa06193e
216	1716	7722268552641777	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj	b8fe1701b02f590e
37	1727	7722268550849956	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj	7a9585b3e7ddf8fd
571	1750	7722268556190014	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj	231778157f603f80
932	1782	7722268559798893	Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj	3a36fd43689f11fb
10	1814	7722268550575638	Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj	fd86315d93b72145
776	1839	7722268558239264	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj	f58a7f4294fda7a3
841	1852	7722268558891201	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj	f80c6a285aeaae37
886	1873	7722268559337288	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj	414d235b5f44abae
791	1896	7722268558396300	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj	b20e36cfdb408fc3
587	1910	7722268556345403	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj	11630bdbe81f3647
821	1928	7722268558689876	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj	728e6f51164102ae
969	1971	7722268560172729	Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj	b82a858f6e15cd45
910	2003	7722268559575878	Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj	36786da85384c296
1057	2025	7722268561051646	Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj	5c2b36d1f4b63b15
1133	2045	7722268561814349	Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj	a2243bfda358accd
995	2060	7722268560430160	Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj	c8c25d7ec8cb0490
1117	2079	7722268561648273	Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj	46266695d45060c5
721	2102	7722268557684712	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj	15353b1b14537456
862	2114	7722268559093257	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj	d494cf7f0148119
1167	2150	7722268562151410	Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj	363133b5dcf9f25b
1234	2187	7722268562824581	Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj	42e0b53fafd40c2f
1259	2264	7722268563067002	Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj	f64c59aa16472238
131	2318	7722268551795331	Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj	858cff15c723fa2
1152	2346	7722268562001261	Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj	c5f45147d67675a0
1182	2366	7722268562296705	Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj	bc449ad297c3f740
1217	2427	7722268562648826	Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj	6a54e761d89b8c88
1192	2467	7722268562400376	Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj	30a3298ce3139518
1852	2835	7722268568999672	Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj	e9a04020749d8098
1087	2845	7722268561356336	Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj	2ce824ec4a54efae
1727	2866	7722268567745551	Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj	4f4c0a2d3645896d
1910	2887	7722268569583832	Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj	31fed0c86b013edf
1716	2917	7722268567641446	Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj	7c2010a28824a800
1815	2950	7722268568636709	Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj	7695b7690bb53fb
1897	3007	7722268569442897	Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj	dd5c6fd2f0d2b543
1928	3030	7722268569765564	Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj	9bad1d12e56aa043
1839	3082	7722268568870129	Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj	112b1e3792b2fd24
2102	3096	7722268571506713	Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj	c250bf863f2632ce
1549	3109	7722268565968822	Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj	9033a8acc6386bcb
1077	3127	7722268561248073	Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj	92c90b89d85a2bef
1873	3164	7722268569212431	Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj	42293fdfd5dac6f1
2004	3236	7722268570513137	Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj	3a3c6806fe135979
1782	3316	7722268568298056	Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj	d185f778a6953f9f
1750	3334	7722268567983674	Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj	60c22710d95bdd45
2025	3398	7722268570731475	Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj	86f509cb80dc118b
2045	3486	7722268570933093	Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj	d51ae8d485f04fff
2150	3513	7722268571978365	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj	b67b4dd33587bcc9
2187	3545	7722268572351390	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj	d639a9de108660bb
2114	3557	7722268571621359	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj	eb9d603a8073d27c
2264	3581	7722268573117406	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj	df33ea1d4a7f8698
1388	3599	7722268564363141	Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj	eed456332973fa3
2346	3622	7722268573936927	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj	e224f0d064e55704
2427	3640	7722268574750014	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj	fd7362daeb898846
2318	3667	7722268573662223	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj	645d70027c000ca0
2079	3706	7722268571268906	Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj	897c2e79ae4cf78
1368	3736	7722268564166590	Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj	a8c3a1db054041ef
2836	3768	7722268578837290	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj	1968b7168309aaaf
2468	3925	7722268575152861	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj	50a916335d4d41f
2887	3984	7722268579352507	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj	524f554e09a68d41
2866	4006	7722268579140089	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj	90099414facf7754
2366	4036	7722268574143814	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj	8469ff10adfb23c8
3109	4072	7722268581574042	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj	7acc8fefc4d7daec
1468	4112	7722268565161626	Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj	8177790848203f5
3007	4176	7722268580552265	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj	ac7a7157827f0ea8
2846	4184	7722268578938106	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj	4524eb825b0530a3
2917	4219	7722268579644254	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj	c49d1221fab5c1a3
3082	4244	7722268581299625	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj	1d2afeb8ebf7277a
1507	4317	7722268565549670	Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj	bcbcfde581dc0a25
3128	4367	7722268581755157	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj	3bdab1c1a691056b
2060	4367	7722268571083005	Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj	1fcd23b833b9dbe1
3334	4379	7722268583820697	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj	415b43c954a892f3
3398	4385	7722268584459302	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj	839ea3bd72e4678d
3164	4414	7722268582124716	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj	4ffaa66fd20d2179
1971	4417	7722268570191348	Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj	c9539fdd203fdf08
3030	4453	7722268580781005	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj	8d16c1cca17f9a6b
3096	4490	7722268581439015	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj	c5c23736b3ce9505
2950	4554	7722268579982743	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj	13cc84af52eb86ba
3236	4651	7722268582837262	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj	477d4373faa6113b
3486	4653	7722268585336973	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj	a9847f32335587e6
3667	4698	7722268587151686	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj	3262c36733127c4a
3317	4753	7722268583645380	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj	24533832719f1fa1
3706	4763	7722268587542726	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj	ff9efcd300bc1147
3736	4808	7722268587839513	Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj	519b5be12af3ee5a
3640	4852	7722268586881157	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj	c803120b92bed2b5
3599	5016	7722268586471740	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj	58d18c44e2affa34
3581	5031	7722268586290413	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj	e64222bb0a52ec7c
3623	5053	7722268586705975	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj	66b14a94618f64bc
3546	5208	7722268585936665	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj	4bc2ebbca9c70677
3514	5351	7722268585616687	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj	71cfa191abaaaa3e
3557	5533	7722268586053406	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj	c9cd2498bb524f78
9	1456	7722295530468079	CMakeFiles/MiWebServer.dir/main.cpp.obj	6a5774972b786948
5533	9168	7722268605811609	Oat++/liboatpp.a	e915e60ae2ff0bd3
1456	4205	7722295544944349	MiWebServer.exe	dffc4bbb68dab149
