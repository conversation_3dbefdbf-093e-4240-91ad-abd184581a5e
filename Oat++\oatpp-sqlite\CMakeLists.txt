#######################################################################################################
## oatpp-sqlite

cmake_minimum_required(VERSION 3.1)

set(OATPP_SQLITE_VERSION "1.3.0")

# Find SQLite3
find_package(PkgConfig QUIET)
if(PKG_CONFIG_FOUND)
    pkg_check_modules(SQLITE3 QUIET sqlite3)
endif()

if(NOT SQLITE3_FOUND)
    find_path(SQLITE3_INCLUDE_DIR sqlite3.h)
    find_library(SQLITE3_LIBRARY sqlite3)
    
    if(SQLITE3_INCLUDE_DIR AND SQLITE3_LIBRARY)
        set(SQLITE3_FOUND TRUE)
        set(SQLITE3_INCLUDE_DIRS ${SQLITE3_INCLUDE_DIR})
        set(SQLITE3_LIBRARIES ${SQLITE3_LIBRARY})
    endif()
endif()

# If SQLite3 is not found, we'll use the embedded version
if(NOT SQLITE3_FOUND)
    message(STATUS "SQLite3 not found, using embedded version")
    set(SQLITE3_INCLUDE_DIRS ${CMAKE_CURRENT_SOURCE_DIR}/sqlite)
    set(SQLITE3_LIBRARIES "")
    set(USE_EMBEDDED_SQLITE TRUE)
else()
    message(STATUS "Found SQLite3: ${SQLITE3_LIBRARIES}")
    set(USE_EMBEDDED_SQLITE FALSE)
endif()

# Create oatpp-sqlite library
add_library(oatpp-sqlite
    src/oatpp-sqlite/Connection.cpp
    src/oatpp-sqlite/Connection.hpp
    src/oatpp-sqlite/ConnectionProvider.cpp
    src/oatpp-sqlite/ConnectionProvider.hpp
    src/oatpp-sqlite/Executor.cpp
    src/oatpp-sqlite/Executor.hpp
    src/oatpp-sqlite/mapping/Deserializer.cpp
    src/oatpp-sqlite/mapping/Deserializer.hpp
    src/oatpp-sqlite/mapping/Serializer.cpp
    src/oatpp-sqlite/mapping/Serializer.hpp
    src/oatpp-sqlite/mapping/type/Blob.hpp
    src/oatpp-sqlite/ql_template/Parser.cpp
    src/oatpp-sqlite/ql_template/Parser.hpp
    src/oatpp-sqlite/ql_template/TemplateValueProvider.cpp
    src/oatpp-sqlite/ql_template/TemplateValueProvider.hpp
)

# Add embedded SQLite if needed
if(USE_EMBEDDED_SQLITE)
    target_sources(oatpp-sqlite PRIVATE
        sqlite/sqlite3.c
        sqlite/sqlite3.h
    )
    target_compile_definitions(oatpp-sqlite PRIVATE SQLITE_ENABLE_JSON1)
endif()

# Set target properties
set_target_properties(oatpp-sqlite PROPERTIES
    CXX_STANDARD 11
    CXX_EXTENSIONS OFF
    CXX_STANDARD_REQUIRED ON
)

if(MSVC)
    target_compile_options(oatpp-sqlite PRIVATE /permissive-)
endif()

# Include directories
target_include_directories(oatpp-sqlite PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
    ${SQLITE3_INCLUDE_DIRS}
)

# Link libraries
target_link_libraries(oatpp-sqlite PUBLIC 
    oatpp
    ${SQLITE3_LIBRARIES}
)

# Add dependencies
add_dependencies(oatpp-sqlite oatpp)
