/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#include "ConnectionProvider.hpp"

namespace oatpp { namespace sqlite {

ConnectionProvider::ConnectionProvider(const oatpp::String& databaseFile, v_int32 flags)
  : m_databaseFile(databaseFile)
  , m_flags(flags)
{}

std::shared_ptr<ConnectionProvider> ConnectionProvider::createShared(const oatpp::String& databaseFile, v_int32 flags) {
  return std::make_shared<ConnectionProvider>(databaseFile, flags);
}

std::shared_ptr<oatpp::orm::Connection> ConnectionProvider::get() {
  
  sqlite3* handle;
  int result = sqlite3_open_v2(m_databaseFile->c_str(), &handle, m_flags, nullptr);
  
  if(result != SQLITE_OK) {
    oatpp::String error = sqlite3_errmsg(handle);
    sqlite3_close(handle);
    throw std::runtime_error("[oatpp::sqlite::ConnectionProvider::get()]: " + *error);
  }
  
  return std::make_shared<Connection>(handle);
}

oatpp::async::CoroutineStarterForResult<const std::shared_ptr<oatpp::orm::Connection>&> ConnectionProvider::getAsync() {
  
  class GetConnectionCoroutine : public oatpp::async::CoroutineWithResult<GetConnectionCoroutine, const std::shared_ptr<oatpp::orm::Connection>&> {
  private:
    std::shared_ptr<ConnectionProvider> m_provider;
    std::shared_ptr<oatpp::orm::Connection> m_connection;
  public:
    
    GetConnectionCoroutine(const std::shared_ptr<ConnectionProvider>& provider)
      : m_provider(provider)
    {}
    
    Action act() override {
      m_connection = m_provider->get();
      return _return(m_connection);
    }
    
  };
  
  return GetConnectionCoroutine::startForResult(std::static_pointer_cast<ConnectionProvider>(shared_from_this()));
}

void ConnectionProvider::stop() {
  // Nothing to stop for SQLite
}

}}
