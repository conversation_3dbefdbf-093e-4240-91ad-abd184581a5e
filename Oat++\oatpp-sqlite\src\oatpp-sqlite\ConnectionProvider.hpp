/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#ifndef oatpp_sqlite_ConnectionProvider_hpp
#define oatpp_sqlite_ConnectionProvider_hpp

#include "Connection.hpp"
#include "oatpp/provider/Provider.hpp"
#include "oatpp/core/Types.hpp"

namespace oatpp { namespace sqlite {

/**
 * SQLite connection provider.
 */
class ConnectionProvider : public oatpp::provider::Provider<oatpp::orm::Connection> {
private:
  oatpp::String m_databaseFile;
  v_int32 m_flags;
public:

  /**
   * Constructor.
   * @param databaseFile - path to database file.
   * @param flags - SQLite flags.
   */
  ConnectionProvider(const oatpp::String& databaseFile, v_int32 flags = SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE);

  /**
   * Create shared ConnectionProvider.
   * @param databaseFile - path to database file.
   * @param flags - SQLite flags.
   * @return - `std::shared_ptr` to ConnectionProvider.
   */
  static std::shared_ptr<ConnectionProvider> createShared(const oatpp::String& databaseFile, 
                                                          v_int32 flags = SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE);

  /**
   * Get connection.
   * @return - `std::shared_ptr` to Connection.
   */
  std::shared_ptr<oatpp::orm::Connection> get() override;

  /**
   * Get connection asynchronously.
   * @return - `oatpp::async::CoroutineStarterForResult` with Connection.
   */
  oatpp::async::CoroutineStarterForResult<const std::shared_ptr<oatpp::orm::Connection>&> getAsync() override;

  /**
   * Stop provider.
   */
  void stop() override;

};

}}

#endif // oatpp_sqlite_ConnectionProvider_hpp
