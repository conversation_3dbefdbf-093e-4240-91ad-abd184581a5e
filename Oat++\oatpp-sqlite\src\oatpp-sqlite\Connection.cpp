/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#include "Connection.hpp"

namespace oatpp { namespace sqlite {

Connection::Connection(sqlite3* handle)
  : m_handle(handle)
  , m_autoCommit(true)
{}

Connection::~Connection() {
  if(m_handle != nullptr) {
    sqlite3_close(m_handle);
  }
}

sqlite3* Connection::getHandle() {
  return m_handle;
}

void Connection::begin() {
  if(m_autoCommit) {
    char* errorMessage = nullptr;
    int result = sqlite3_exec(m_handle, "BEGIN TRANSACTION", nullptr, nullptr, &errorMessage);
    if(result != SQLITE_OK) {
      oatpp::String error = errorMessage;
      sqlite3_free(errorMessage);
      throw std::runtime_error("[oatpp::sqlite::Connection::begin()]: " + *error);
    }
    m_autoCommit = false;
  }
}

void Connection::commit() {
  if(!m_autoCommit) {
    char* errorMessage = nullptr;
    int result = sqlite3_exec(m_handle, "COMMIT", nullptr, nullptr, &errorMessage);
    if(result != SQLITE_OK) {
      oatpp::String error = errorMessage;
      sqlite3_free(errorMessage);
      throw std::runtime_error("[oatpp::sqlite::Connection::commit()]: " + *error);
    }
    m_autoCommit = true;
  }
}

void Connection::rollback() {
  if(!m_autoCommit) {
    char* errorMessage = nullptr;
    int result = sqlite3_exec(m_handle, "ROLLBACK", nullptr, nullptr, &errorMessage);
    if(result != SQLITE_OK) {
      oatpp::String error = errorMessage;
      sqlite3_free(errorMessage);
      throw std::runtime_error("[oatpp::sqlite::Connection::rollback()]: " + *error);
    }
    m_autoCommit = true;
  }
}

bool Connection::isAutoCommit() {
  return m_autoCommit;
}

bool Connection::isClosed() {
  return m_handle == nullptr;
}

void Connection::close() {
  if(m_handle != nullptr) {
    sqlite3_close(m_handle);
    m_handle = nullptr;
  }
}

}}
